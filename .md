var TSC = TSC || {};

TSC.embedded_config_xml = '<x:xmpmeta tsc:version="2.0.1" xmlns:x="adobe:ns:meta/" xmlns:tsc="http://www.techsmith.com/xmp/tsc/">\
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xmp="http://ns.adobe.com/xap/1.0/" xmlns:xmpDM="http://ns.adobe.com/xmp/1.0/DynamicMedia/" xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/" xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/" xmlns:tscDM="http://www.techsmith.com/xmp/tscDM/" xmlns:tscIQ="http://www.techsmith.com/xmp/tscIQ/" xmlns:tscHS="http://www.techsmith.com/xmp/tscHS/" xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#" xmlns:stFnt="http://ns.adobe.com/xap/1.0/sType/Font#" xmlns:exif="http://ns.adobe.com/exif/1.0" xmlns:dc="http://purl.org/dc/elements/1.1/">\
      <rdf:Description dc:date="2024-12-08 09:41:26 PM" dc:source="Camtasia,24.1.3,enu" dc:title="Case5_DeliquenyChangeTHreshold" tscDM:firstFrame="Case5_DeliquenyChangeTHreshold_First_Frame.png" tscDM:project="Case5_AggregrateChange">\
         <xmpDM:duration xmpDM:scale="1/1000" xmpDM:value="32566"/>\
         <xmpDM:videoFrameSize stDim:unit="pixel" stDim:h="720" stDim:w="1280"/>\
         <tsc:langName>\
            <rdf:Bag>\
               <rdf:li xml:lang="en-US">English</rdf:li></rdf:Bag>\
         </tsc:langName>\
         <xmpDM:Tracks>\
            <rdf:Bag>\
               <rdf:li>\
                  <rdf:Description xmpDM:trackType="Hotspot" xmpDM:frameRate="f1000" xmpDM:trackName="Hotspots">\
                     <xmpDM:markers>\
                        <rdf:Seq>\
                           <rdf:li><rdf:Description xmpDM:label="1" xmpDM:startTime="7270" xmpDM:duration="400" tscDM:boundingPoly="198,452;420,452;420,542;198,542;" tscDM:rotate="0.000000" tscHS:pause="1" tscHS:jumpTime="7767"/></rdf:li><rdf:li><rdf:Description xmpDM:label="2" xmpDM:startTime="9600" xmpDM:duration="530" tscDM:boundingPoly="187,139;435,139;435,222;187,222;" tscDM:rotate="0.000000" tscHS:pause="1" tscHS:jumpTime="10167"/></rdf:li><rdf:li><rdf:Description xmpDM:label="3" xmpDM:startTime="17530" xmpDM:duration="1270" tscDM:boundingPoly="149,197;469,197;469,284;149,284;" tscDM:rotate="0.000000" tscHS:pause="1" tscHS:jumpTime="23733"/></rdf:li><rdf:li><rdf:Description xmpDM:label="4" xmpDM:startTime="21070" xmpDM:duration="500" tscDM:boundingPoly="557,518;771,518;771,587;557,587;" tscDM:rotate="0.000000" tscHS:pause="1" tscHS:jumpTime="21767"/></rdf:li></rdf:Seq>\
                     </xmpDM:markers>\
                  </rdf:Description>\
               </rdf:li>\
            </rdf:Bag>\
         </xmpDM:Tracks>\
         <tscDM:controller>\
            <rdf:Description xmpDM:name="tscplayer">\
               <tscDM:parameters>\
                  <rdf:Bag>\
                     <rdf:li xmpDM:name="autohide" xmpDM:value="true"/><rdf:li xmpDM:name="autoplay" xmpDM:value="false"/><rdf:li xmpDM:name="loop" xmpDM:value="false"/><rdf:li xmpDM:name="searchable" xmpDM:value="false"/><rdf:li xmpDM:name="captionsenabled" xmpDM:value="false"/><rdf:li xmpDM:name="sidebarenabled" xmpDM:value="false"/><rdf:li xmpDM:name="unicodeenabled" xmpDM:value="false"/><rdf:li xmpDM:name="backgroundcolor" xmpDM:value="FFFFFF"/><rdf:li xmpDM:name="sidebarlocation" xmpDM:value="left"/><rdf:li xmpDM:name="endaction" xmpDM:value="stop"/><rdf:li xmpDM:name="endactionparam" xmpDM:value="false"/><rdf:li xmpDM:name="locale" xmpDM:value="en-US"/></rdf:Bag>\
               </tscDM:parameters>\
               <tscDM:controllerText>\
                  <rdf:Bag>\
                  </rdf:Bag>\
               </tscDM:controllerText>\
            </rdf:Description>\
         </tscDM:controller>\
         <tscDM:contentList>\
            <rdf:Description>\
               <tscDM:files>\
                  <rdf:Seq>\
                     <rdf:li xmpDM:name="0" xmpDM:value="Case5_DeliquenyChangeTHreshold_First_Frame.png"/><rdf:li xmpDM:name="1" xmpDM:value="Case5_DeliquenyChangeTHreshold_Thumbnails.png"/></rdf:Seq>\
               </tscDM:files>\
            </rdf:Description>\
         </tscDM:contentList>\
      </rdf:Description>\
   </rdf:RDF>\
</x:xmpmeta>';



<x:xmpmeta tsc:version="2.0.1" xmlns:x="adobe:ns:meta/" xmlns:tsc="http://www.techsmith.com/xmp/tsc/">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xmp="http://ns.adobe.com/xap/1.0/" xmlns:xmpDM="http://ns.adobe.com/xmp/1.0/DynamicMedia/" xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/" xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/" xmlns:tscDM="http://www.techsmith.com/xmp/tscDM/" xmlns:tscIQ="http://www.techsmith.com/xmp/tscIQ/" xmlns:tscHS="http://www.techsmith.com/xmp/tscHS/" xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#" xmlns:stFnt="http://ns.adobe.com/xap/1.0/sType/Font#" xmlns:exif="http://ns.adobe.com/exif/1.0" xmlns:dc="http://purl.org/dc/elements/1.1/">
      <rdf:Description dc:date="2024-12-08 09:41:26 PM" dc:source="Camtasia,24.1.3,enu" dc:title="Case5_DeliquenyChangeTHreshold" tscDM:firstFrame="Case5_DeliquenyChangeTHreshold_First_Frame.png" tscDM:project="Case5_AggregrateChange">
         <xmpDM:duration xmpDM:scale="1/1000" xmpDM:value="32566"/>
         <xmpDM:videoFrameSize stDim:unit="pixel" stDim:h="720" stDim:w="1280"/>
         <tsc:langName>
            <rdf:Bag>
               <rdf:li xml:lang="en-US">English</rdf:li></rdf:Bag>
         </tsc:langName>
         <xmpDM:Tracks>
            <rdf:Bag>
               <rdf:li>
                  <rdf:Description xmpDM:trackType="Hotspot" xmpDM:frameRate="f1000" xmpDM:trackName="Hotspots">
                     <xmpDM:markers>
                        <rdf:Seq>
                           <rdf:li><rdf:Description xmpDM:label="1" xmpDM:startTime="7270" xmpDM:duration="400" tscDM:boundingPoly="198,452;420,452;420,542;198,542;" tscDM:rotate="0.000000" tscHS:pause="1" tscHS:jumpTime="7767"/></rdf:li><rdf:li><rdf:Description xmpDM:label="2" xmpDM:startTime="9600" xmpDM:duration="530" tscDM:boundingPoly="187,139;435,139;435,222;187,222;" tscDM:rotate="0.000000" tscHS:pause="1" tscHS:jumpTime="10167"/></rdf:li><rdf:li><rdf:Description xmpDM:label="3" xmpDM:startTime="17530" xmpDM:duration="1270" tscDM:boundingPoly="149,197;469,197;469,284;149,284;" tscDM:rotate="0.000000" tscHS:pause="1" tscHS:jumpTime="23733"/></rdf:li><rdf:li><rdf:Description xmpDM:label="4" xmpDM:startTime="21070" xmpDM:duration="500" tscDM:boundingPoly="557,518;771,518;771,587;557,587;" tscDM:rotate="0.000000" tscHS:pause="1" tscHS:jumpTime="21767"/></rdf:li></rdf:Seq>
                     </xmpDM:markers>
                  </rdf:Description>
               </rdf:li>
            </rdf:Bag>
         </xmpDM:Tracks>
         <tscDM:controller>
            <rdf:Description xmpDM:name="tscplayer">
               <tscDM:parameters>
                  <rdf:Bag>
                     <rdf:li xmpDM:name="autohide" xmpDM:value="true"/><rdf:li xmpDM:name="autoplay" xmpDM:value="false"/><rdf:li xmpDM:name="loop" xmpDM:value="false"/><rdf:li xmpDM:name="searchable" xmpDM:value="false"/><rdf:li xmpDM:name="captionsenabled" xmpDM:value="false"/><rdf:li xmpDM:name="sidebarenabled" xmpDM:value="false"/><rdf:li xmpDM:name="unicodeenabled" xmpDM:value="false"/><rdf:li xmpDM:name="backgroundcolor" xmpDM:value="FFFFFF"/><rdf:li xmpDM:name="sidebarlocation" xmpDM:value="left"/><rdf:li xmpDM:name="endaction" xmpDM:value="stop"/><rdf:li xmpDM:name="endactionparam" xmpDM:value="false"/><rdf:li xmpDM:name="locale" xmpDM:value="en-US"/></rdf:Bag>
               </tscDM:parameters>
               <tscDM:controllerText>
                  <rdf:Bag>
                  </rdf:Bag>
               </tscDM:controllerText>
            </rdf:Description>
         </tscDM:controller>
         <tscDM:contentList>
            <rdf:Description>
               <tscDM:files>
                  <rdf:Seq>
                     <rdf:li xmpDM:name="0" xmpDM:value="Case5_DeliquenyChangeTHreshold_First_Frame.png"/><rdf:li xmpDM:name="1" xmpDM:value="Case5_DeliquenyChangeTHreshold_Thumbnails.png"/></rdf:Seq>
               </tscDM:files>
            </rdf:Description>
         </tscDM:contentList>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>



@charset "utf-8";

html, body {
  margin: 0;
  width: 100%;
  text-align: center;
  background-color: #1a1a1a;
  box-sizing: border-box;
  overflow: hidden;
}

.smart-player-embed-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
  height: 0;
  overflow: hidden;
  padding-top: calc(720 / 1280 * 100%);
  position: relative;
}

.smart-player-embed-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  max-width: 1280px;
  max-height: 720px;
}

.smart-player-embed-iframe:-ms-fullscreen { 
  max-width: 100%;
  max-height: 100%;
}

@media screen and ( max-height: 720px )
{
   .smart-player-embed-iframe {
     max-height: 100vh;
   }
}

<!DOCTYPE html>
<!-- saved from url=(0014)about:internet -->
<html>
<head>
<meta name="google" value="notranslate" /> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title></title>
<link href="https://cdn.cloud.techsmith.com/smartplayer/5/latest/techsmith-smart-player.min.css" rel="stylesheet" type="text/css" />

<style>
html, body {
    background-color: #1a1a1a;
}
</style>
</head>
<body>

<div id="tscVideoContent">
    <img width="32px" height="32px" style="position: absolute; top: 50%; left: 50%; margin: -16px 0 0 -16px"
         src="data:image/gif;base64,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">
</div>

<script src="scripts/config_xml.js"></script>
<script type="text/javascript">
    (function (window) {
        function setup(TSC) {
             TSC.playerConfiguration.addMediaSrc("Case5_DeliquenyChangeTHreshold.mp4");
             TSC.playerConfiguration.setXMPSrc("Case5_DeliquenyChangeTHreshold_config.xml");
             TSC.playerConfiguration.setAutoHideControls(true);
             TSC.playerConfiguration.setBackgroundColor("#FFFFFF");
             TSC.playerConfiguration.setCaptionsEnabled(false);
             TSC.playerConfiguration.setSidebarEnabled(false);
             TSC.playerConfiguration.setTheme(TSC.themeNames.DUSK);
             TSC.playerConfiguration.allowFastForward = false;
             TSC.playerConfiguration.skipVideoControlsEnabled = false;
             TSC.playerConfiguration.skipVideoDuration = 10;
             
             TSC.playerConfiguration.setAutoPlayMedia(false);
             TSC.playerConfiguration.setPosterImageSrc("Case5_DeliquenyChangeTHreshold_First_Frame.png");
             TSC.playerConfiguration.setIsSearchable(false);
             TSC.playerConfiguration.setEndActionType("stop");
             TSC.playerConfiguration.setEndActionParam("false");
             TSC.playerConfiguration.setAllowRewind(-1);
             

             TSC.localizationStrings.setLanguage(TSC.languageCodes.ENGLISH);

            TSC.mediaPlayer.init("#tscVideoContent");
        }

        function loadScript(e,t){if(!e||!(typeof e==="string")){return}var n=document.createElement("script");if(typeof document.attachEvent==="object"){n.onreadystatechange=function(){if(n.readyState==="complete"||n.readyState==="loaded"){if(t){t()}}}}else{n.onload=function(){if(t){t()}}}n.src=e;document.getElementsByTagName("head")[0].appendChild(n)}

        loadScript('https://cdn.cloud.techsmith.com/smartplayer/5/latest/techsmith-smart-player.min.js', function() {
            setup(window["TSC"]);
        });
    }(window));
</script>
</body>
</html>
<!DOCTYPE HTML>
<html>
<head>

<meta name="google" value="notranslate" /> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<title>Created by Camtasia 2024</title>

<link href="Case5_DeliquenyChangeTHreshold_embed.css" rel="stylesheet" type="text/css">
</head>
<body>
<div class="smart-player-embed-container">
<iframe class="smart-player-embed-iframe" id="embeddedSmartPlayerInstance" src="Case5_DeliquenyChangeTHreshold_player.html?embedIFrameId=embeddedSmartPlayerInstance" scrolling="no" frameborder="0" webkitAllowFullScreen mozallowfullscreen allowFullScreen></iframe>
</div>
<script src="https://cdn.cloud.techsmith.com/smartplayer/5/latest/embedded-smart-player.min.js"></script>

</body>
</html>


